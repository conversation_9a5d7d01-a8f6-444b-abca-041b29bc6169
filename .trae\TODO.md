# TODO:

- [x] 1: Configurar Supabase e criar estrutura do banco de dados (priority: High)
- [ ] 2: Implementar sistema de autenticação (login/logout) (**IN PROGRESS**) (priority: High)
- [ ] 3: Criar layout base e sistema de navegação (priority: High)
- [ ] 5: <PERSON><PERSON>r gerenciador de mídia com upload de arquivos (priority: High)
- [ ] 6: Implementar editor de playlists com drag-and-drop (priority: High)
- [ ] 7: Criar player p<PERSON><PERSON><PERSON><PERSON> fullscreen (priority: High)
- [ ] 8: Implementar APIs backend completas (priority: High)
- [ ] 4: Implementar dashboard do cliente (priority: Medium)
- [ ] 9: Criar monitor de telas ativas (priority: Medium)
- [ ] 10: Implementar painel administrativo (priority: Medium)
