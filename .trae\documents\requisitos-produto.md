# Sistema de Gerenciamento de Programação Multimídia para TV Corporativa

## 1. Visão Geral do Produto

Sistema web para criação e gerenciamento de programação multimídia destinada a TVs corporativas, permitindo que clientes criem playlists com vídeos, imagens, mensagens e templates HTML, gerem links públicos e executem a programação em qualquer dispositivo com navegador.

O produto resolve o problema de distribuição de conteúdo corporativo para múltiplas telas de forma centralizada e automatizada, sendo utilizado por empresas que precisam exibir conteúdo em TVs, totens digitais, Raspberry Pi e outros dispositivos.

O sistema visa democratizar a comunicação visual corporativa, oferecendo uma solução simples e escalável para gestão de conteúdo digital.

## 2. Funcionalidades Principais

### 2.1 Papéis de Usuário

| Papel | Método de Cadastro | Permissões Principais |
|-------|-------------------|----------------------|
| Cliente | Registro por email | Criar playlists, fazer upload de mídia, gerar links públicos, visualizar estatísticas |
| Administrador | Criação manual pelo sistema | Gerenciar usuários, monitorar sistema, acessar logs, controlar armazenamento |

### 2.2 Módulos de Funcionalidade

Nosso sistema de gerenciamento multimídia consiste nas seguintes páginas principais:

1. **Página de Login**: autenticação de usuários, recuperação de senha.
2. **Dashboard do Cliente**: visão geral das playlists, estatísticas de reprodução, telas ativas.
3. **Gerenciador de Mídia**: upload e organização de vídeos, imagens, mensagens e templates.
4. **Editor de Playlists**: criação e edição de playlists com drag-and-drop, definição de duração e ordem.
5. **Configurações de Programação**: agendamento por dia da semana e horários específicos.
6. **Monitor de Telas**: visualização de dispositivos ativos, status de conexão, logs de exibição.
7. **Player Público**: reprodução fullscreen da playlist em dispositivos finais.
8. **Painel Administrativo**: gestão de usuários, monitoramento do sistema, controle de armazenamento.

### 2.3 Detalhes das Páginas

| Nome da Página | Nome do Módulo | Descrição da Funcionalidade |
|----------------|----------------|-----------------------------|
| Página de Login | Sistema de Autenticação | Autenticar usuários via email/senha, recuperar senha por email, manter sessão ativa |
| Dashboard do Cliente | Painel Principal | Exibir resumo de playlists ativas, estatísticas de reprodução, alertas de telas offline |
| Gerenciador de Mídia | Upload de Arquivos | Fazer upload de vídeos MP4, imagens JPG/PNG, criar mensagens de texto, editar templates HTML |
| Gerenciador de Mídia | Biblioteca de Mídia | Organizar arquivos em pastas, visualizar previews, excluir conteúdo, buscar por nome |
| Editor de Playlists | Construtor de Playlist | Arrastar e soltar itens de mídia, definir ordem de reprodução, configurar duração de cada item |
| Editor de Playlists | Gerador de Links | Criar links públicos únicos, configurar permissões de acesso, copiar URL para compartilhamento |
| Configurações de Programação | Agendador | Programar playlists por dia da semana, definir horários específicos, configurar loops |
| Monitor de Telas | Status de Dispositivos | Visualizar telas conectadas, último ping recebido, status online/offline em tempo real |
| Monitor de Telas | Logs de Reprodução | Registrar histórico de reprodução, identificar erros, monitorar performance |
| Player Público | Reprodutor de Mídia | Executar playlist em fullscreen, alternar entre vídeos/imagens/mensagens automaticamente |
| Player Público | Sistema de Cache | Armazenar conteúdo localmente, sincronizar atualizações, garantir reprodução offline |
| Player Público | Reconexão Automática | Detectar perda de conexão, tentar reconectar automaticamente, reportar status |
| Painel Administrativo | Gestão de Usuários | Criar, editar e excluir contas de usuário, definir permissões, resetar senhas |
| Painel Administrativo | Monitoramento do Sistema | Visualizar logs do sistema, monitorar uso de armazenamento, gerar relatórios |
| Painel Administrativo | Backup e Manutenção | Realizar backup da base de dados, limpar arquivos órfãos, otimizar performance |

## 3. Processo Principal

**Fluxo do Cliente:**
1. Cliente faz login no sistema
2. Acessa o gerenciador de mídia e faz upload de conteúdo (vídeos, imagens, mensagens, templates)
3. Utiliza o editor de playlists para criar uma programação, arrastando itens e definindo durações
4. Configura programação por horários e dias da semana se necessário
5. Gera link público da playlist
6. Compartilha o link com dispositivos de reprodução
7. Monitora telas ativas e logs de reprodução

**Fluxo do Player:**
1. Dispositivo acessa link público da playlist
2. Player carrega em modo fullscreen
3. Faz cache do conteúdo da playlist
4. Inicia reprodução automática seguindo ordem e durações definidas
5. Envia pings periódicos para reportar status
6. Reconecta automaticamente em caso de perda de conexão

**Fluxo do Administrador:**
1. Admin acessa painel administrativo
2. Gerencia usuários e suas permissões
3. Monitora uso do sistema e performance
4. Visualiza logs e relatórios
5. Realiza manutenção e backups

```mermaid
graph TD
    A[Login] --> B[Dashboard Cliente]
    B --> C[Gerenciador de Mídia]
    C --> D[Editor de Playlists]
    D --> E[Configurações de Programação]
    E --> F[Geração de Link Público]
    F --> G[Player Público]
    B --> H[Monitor de Telas]
    A --> I[Painel Administrativo]
    I --> J[Gestão de Usuários]
    I --> K[Monitoramento do Sistema]
```

## 4. Design da Interface do Usuário

### 4.1 Estilo de Design

- **Cores Primárias**: Azul corporativo (#2563eb), branco (#ffffff)
- **Cores Secundárias**: Cinza claro (#f8fafc), cinza escuro (#1e293b), verde sucesso (#10b981), vermelho erro (#ef4444)
- **Estilo de Botões**: Arredondados com bordas suaves, efeito hover sutil
- **Fontes**: Inter ou similar, tamanhos 14px (corpo), 16px (botões), 24px (títulos)
- **Layout**: Design baseado em cards, navegação lateral fixa, layout responsivo
- **Ícones**: Lucide React ou Heroicons, estilo outline, tamanho 20px padrão

### 4.2 Visão Geral do Design das Páginas

| Nome da Página | Nome do Módulo | Elementos da UI |
|----------------|----------------|----------------|
| Página de Login | Formulário de Login | Card centralizado, campos de input com ícones, botão primário azul, link de recuperação |
| Dashboard do Cliente | Cards de Estatísticas | Grid de cards com métricas, gráficos simples, lista de playlists recentes |
| Gerenciador de Mídia | Área de Upload | Drag-and-drop zone, progress bars, grid de thumbnails, modal de preview |
| Editor de Playlists | Interface Drag-Drop | Sidebar com biblioteca, área central de construção, timeline horizontal |
| Monitor de Telas | Tabela de Status | Tabela responsiva, indicadores de status coloridos, botões de ação |
| Player Público | Reprodutor Fullscreen | Interface minimalista, controles ocultos, transições suaves entre conteúdos |
| Painel Administrativo | Dashboard Admin | Layout de duas colunas, tabelas de dados, modais para ações |

### 4.3 Responsividade

O produto é desktop-first com adaptação completa para mobile e tablets. O player público é otimizado para touch em dispositivos móveis, com gestos para navegação. A interface administrativa prioriza desktop mas mantém funcionalidade em tablets.