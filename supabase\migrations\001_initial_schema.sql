-- <PERSON><PERSON><PERSON> tabel<PERSON> de usuários
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'client' CHECK (role IN ('client', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança Supabase
GRANT SELECT ON users TO anon;
GRANT ALL PRIVILEGES ON users TO authenticated;

-- Índices
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);

-- <PERSON><PERSON>r tabela de mídia
CREATE TABLE media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('video', 'image', 'message', 'template')),
    file_path VARCHAR(500),
    file_url VARCHAR(500),
    file_size INTEGER,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON media TO anon;
GRANT ALL PRIVILEGES ON media TO authenticated;

-- Índices
CREATE INDEX idx_media_user_id ON media(user_id);
CREATE INDEX idx_media_type ON media(type);
CREATE INDEX idx_media_created_at ON media(created_at DESC);

-- Criar tabela de playlists
CREATE TABLE playlists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    public_id VARCHAR(50) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON playlists TO anon;
GRANT ALL PRIVILEGES ON playlists TO authenticated;

-- Índices
CREATE INDEX idx_playlists_user_id ON playlists(user_id);
CREATE INDEX idx_playlists_public_id ON playlists(public_id);
CREATE INDEX idx_playlists_is_active ON playlists(is_active);

-- Criar tabela de itens da playlist
CREATE TABLE playlist_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    media_id UUID NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL,
    duration_seconds INTEGER DEFAULT 10,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON playlist_items TO anon;
GRANT ALL PRIVILEGES ON playlist_items TO authenticated;

-- Índices
CREATE INDEX idx_playlist_items_playlist_id ON playlist_items(playlist_id);
CREATE INDEX idx_playlist_items_order ON playlist_items(playlist_id, order_index);

-- Criar tabela de programação
CREATE TABLE playlist_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    day_of_week VARCHAR(10) NOT NULL CHECK (day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON playlist_schedules TO anon;
GRANT ALL PRIVILEGES ON playlist_schedules TO authenticated;

-- Índices
CREATE INDEX idx_playlist_schedules_playlist_id ON playlist_schedules(playlist_id);
CREATE INDEX idx_playlist_schedules_day ON playlist_schedules(day_of_week);

-- Criar tabela de sessões de tela
CREATE TABLE screen_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    screen_id VARCHAR(100) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    last_ping TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'error')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON screen_sessions TO anon;
GRANT ALL PRIVILEGES ON screen_sessions TO authenticated;

-- Índices
CREATE INDEX idx_screen_sessions_playlist_id ON screen_sessions(playlist_id);
CREATE INDEX idx_screen_sessions_screen_id ON screen_sessions(screen_id);
CREATE INDEX idx_screen_sessions_last_ping ON screen_sessions(last_ping DESC);

-- Criar tabela de logs de tela
CREATE TABLE screen_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES screen_sessions(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON screen_logs TO anon;
GRANT ALL PRIVILEGES ON screen_logs TO authenticated;

-- Índices
CREATE INDEX idx_screen_logs_session_id ON screen_logs(session_id);
CREATE INDEX idx_screen_logs_event_type ON screen_logs(event_type);
CREATE INDEX idx_screen_logs_created_at ON screen_logs(created_at DESC);

-- Inserir usuário administrador padrão
INSERT INTO users (email, password_hash, name, role) VALUES 
('<EMAIL>', '$2b$10$exemplo_hash_senha', 'Administrador', 'admin');

-- Inserir usuário cliente de exemplo
INSERT INTO users (email, password_hash, name, role) VALUES 
('<EMAIL>', '$2b$10$exemplo_hash_senha', 'Cliente Exemplo', 'client');