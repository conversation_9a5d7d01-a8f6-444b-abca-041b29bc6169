# Arquitetura Técnica - Sistema de Gerenciamento Multimídia

## 1. Design da Arquitetura

```mermaid
graph TD
    A[Navegador do Cliente] --> B[React Frontend Application]
    B --> C[Node.js/Express Backend]
    C --> D[Supabase Database]
    C --> E[Sistema de Arquivos Local]
    C --> F[Redis Cache]
    
    G[Player TV/Dispositivo] --> H[Player React App]
    H --> C
    
    subgraph "Frontend Layer"
        B
        H
    end
    
    subgraph "Backend Layer"
        C
        F
    end
    
    subgraph "Data Layer"
        D
        E
    end
```

## 2. Descrição das Tecnologias

* **Frontend Cliente**: React\@18 + TypeScript + Tailwind CSS + Vite + React Router + React DnD

* **Frontend Player**: React\@18 + TypeScript + Tailwind CSS + Vite (build otimizado)

* **Backend**: Node.js\@20 + Express\@4 + TypeScript + Multer + Socket.io

* **Banco de Dados**: Supabase (PostgreSQL) + Redis para cache

* **Armazenamento**: Sistema de arquivos local + Supabase Storage para backup

* **Autenticação**: Supabase Auth

## 3. Definições de Rotas

### 3.1 Frontend Cliente

| Rota                    | Propósito                                      |
| ----------------------- | ---------------------------------------------- |
| /login                  | Página de autenticação de usuários             |
| /dashboard              | Dashboard principal do cliente com visão geral |
| /media                  | Gerenciador de mídia para upload e organização |
| /playlists              | Lista e gerenciamento de playlists             |
| /playlists/:id/edit     | Editor de playlist específica                  |
| /playlists/:id/schedule | Configurações de programação da playlist       |
| /screens                | Monitor de telas ativas e logs                 |
| /admin                  | Painel administrativo (apenas admins)          |
| /admin/users            | Gestão de usuários                             |
| /admin/system           | Monitoramento do sistema                       |

### 3.2 Frontend Player

| Rota                   | Propósito                            |
| ---------------------- | ------------------------------------ |
| /p/:playlistId         | Player público da playlist           |
| /p/:playlistId/preview | Preview da playlist (sem fullscreen) |

## 4. Definições de API

### 4.1 APIs Principais

**Autenticação**

```
POST /api/auth/login
```

Request:

| Nome do Parâmetro | Tipo   | Obrigatório | Descrição        |
| ----------------- | ------ | ----------- | ---------------- |
| email             | string | true        | Email do usuário |
| password          | string | true        | Senha do usuário |

Response:

| Nome do Parâmetro | Tipo    | Descrição              |
| ----------------- | ------- | ---------------------- |
| success           | boolean | Status da autenticação |
| token             | string  | JWT token de acesso    |
| user              | object  | Dados do usuário       |

**Upload de Mídia**

```
POST /api/media/upload
```

Request:

| Nome do Parâmetro | Tipo   | Obrigatório | Descrição                                       |
| ----------------- | ------ | ----------- | ----------------------------------------------- |
| file              | File   | true        | Arquivo de mídia (vídeo/imagem)                 |
| type              | string | true        | Tipo de mídia (video, image, message, template) |
| name              | string | false       | Nome personalizado                              |

Response:

| Nome do Parâmetro | Tipo    | Descrição             |
| ----------------- | ------- | --------------------- |
| success           | boolean | Status do upload      |
| media             | object  | Dados da mídia criada |

**Gestão de Playlists**

```
GET /api/playlists
POST /api/playlists
PUT /api/playlists/:id
DELETE /api/playlists/:id
```

**Player Público**

```
GET /api/player/:playlistId
```

Response:

| Nome do Parâmetro | Tipo   | Descrição                    |
| ----------------- | ------ | ---------------------------- |
| playlist          | object | Dados da playlist            |
| items             | array  | Lista de itens de mídia      |
| schedule          | object | Configurações de programação |

**Monitoramento de Telas**

```
POST /api/screens/ping
```

Request:

| Nome do Parâmetro | Tipo   | Obrigatório | Descrição                             |
| ----------------- | ------ | ----------- | ------------------------------------- |
| playlistId        | string | true        | ID da playlist em reprodução          |
| screenId          | string | true        | ID único da tela                      |
| status            | string | true        | Status atual (playing, paused, error) |

## 5. Arquitetura do Servidor

```mermaid
graph TD
    A[Cliente/Frontend] --> B[Middleware de Autenticação]
    B --> C[Controller Layer]
    C --> D[Service Layer]
    D --> E[Repository Layer]
    E --> F[(Supabase Database)]
    
    C --> G[File Storage Service]
    G --> H[Sistema de Arquivos]
    
    C --> I[Cache Service]
    I --> J[(Redis)]
    
    C --> K[Socket.io Service]
    K --> L[WebSocket Connections]
    
    subgraph Servidor
        B
        C
        D
        E
        G
        I
        K
    end
```

## 6. Modelo de Dados

### 6.1 Definição do Modelo de Dados

```mermaid
erDiagram
    USERS ||--o{ PLAYLISTS : creates
    PLAYLISTS ||--o{ PLAYLIST_ITEMS : contains
    PLAYLISTS ||--o{ PLAYLIST_SCHEDULES : has
    PLAYLISTS ||--o{ SCREEN_SESSIONS : plays_on
    MEDIA ||--o{ PLAYLIST_ITEMS : used_in
    USERS ||--o{ MEDIA : uploads
    SCREEN_SESSIONS ||--o{ SCREEN_LOGS : generates
    
    USERS {
        uuid id PK
        string email
        string password_hash
        string name
        string role
        timestamp created_at
        timestamp updated_at
    }
    
    MEDIA {
        uuid id PK
        uuid user_id FK
        string name
        string type
        string file_path
        string file_url
        integer file_size
        json metadata
        timestamp created_at
    }
    
    PLAYLISTS {
        uuid id PK
        uuid user_id FK
        string name
        string description
        string public_id
        boolean is_active
        json settings
        timestamp created_at
        timestamp updated_at
    }
    
    PLAYLIST_ITEMS {
        uuid id PK
        uuid playlist_id FK
        uuid media_id FK
        integer order_index
        integer duration_seconds
        json settings
        timestamp created_at
    }
    
    PLAYLIST_SCHEDULES {
        uuid id PK
        uuid playlist_id FK
        string day_of_week
        time start_time
        time end_time
        boolean is_active
        timestamp created_at
    }
    
    SCREEN_SESSIONS {
        uuid id PK
        uuid playlist_id FK
        string screen_id
        string ip_address
        string user_agent
        timestamp last_ping
        string status
        timestamp created_at
    }
    
    SCREEN_LOGS {
        uuid id PK
        uuid session_id FK
        string event_type
        json event_data
        timestamp created_at
    }
```

### 6.2 Linguagem de Definição de Dados

**Tabela de Usuários (users)**

```sql
-- Criar tabela de usuários
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(20) DEFAULT 'client' CHECK (role IN ('client', 'admin')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança Supabase
GRANT SELECT ON users TO anon;
GRANT ALL PRIVILEGES ON users TO authenticated;

-- Índices
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
```

**Tabela de Mídia (media)**

```sql
-- Criar tabela de mídia
CREATE TABLE media (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(20) NOT NULL CHECK (type IN ('video', 'image', 'message', 'template')),
    file_path VARCHAR(500),
    file_url VARCHAR(500),
    file_size INTEGER,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON media TO anon;
GRANT ALL PRIVILEGES ON media TO authenticated;

-- Índices
CREATE INDEX idx_media_user_id ON media(user_id);
CREATE INDEX idx_media_type ON media(type);
CREATE INDEX idx_media_created_at ON media(created_at DESC);
```

**Tabela de Playlists (playlists)**

```sql
-- Criar tabela de playlists
CREATE TABLE playlists (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    public_id VARCHAR(50) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON playlists TO anon;
GRANT ALL PRIVILEGES ON playlists TO authenticated;

-- Índices
CREATE INDEX idx_playlists_user_id ON playlists(user_id);
CREATE INDEX idx_playlists_public_id ON playlists(public_id);
CREATE INDEX idx_playlists_is_active ON playlists(is_active);
```

**Tabela de Itens da Playlist (playlist\_items)**

```sql
-- Criar tabela de itens da playlist
CREATE TABLE playlist_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    media_id UUID NOT NULL REFERENCES media(id) ON DELETE CASCADE,
    order_index INTEGER NOT NULL,
    duration_seconds INTEGER DEFAULT 10,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON playlist_items TO anon;
GRANT ALL PRIVILEGES ON playlist_items TO authenticated;

-- Índices
CREATE INDEX idx_playlist_items_playlist_id ON playlist_items(playlist_id);
CREATE INDEX idx_playlist_items_order ON playlist_items(playlist_id, order_index);
```

**Tabela de Programação (playlist\_schedules)**

```sql
-- Criar tabela de programação
CREATE TABLE playlist_schedules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    day_of_week VARCHAR(10) NOT NULL CHECK (day_of_week IN ('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON playlist_schedules TO anon;
GRANT ALL PRIVILEGES ON playlist_schedules TO authenticated;

-- Índices
CREATE INDEX idx_playlist_schedules_playlist_id ON playlist_schedules(playlist_id);
CREATE INDEX idx_playlist_schedules_day ON playlist_schedules(day_of_week);
```

**Tabela de Sessões de Tela (screen\_sessions)**

```sql
-- Criar tabela de sessões de tela
CREATE TABLE screen_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    playlist_id UUID NOT NULL REFERENCES playlists(id) ON DELETE CASCADE,
    screen_id VARCHAR(100) NOT NULL,
    ip_address INET,
    user_agent TEXT,
    last_ping TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'error')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON screen_sessions TO anon;
GRANT ALL PRIVILEGES ON screen_sessions TO authenticated;

-- Índices
CREATE INDEX idx_screen_sessions_playlist_id ON screen_sessions(playlist_id);
CREATE INDEX idx_screen_sessions_screen_id ON screen_sessions(screen_id);
CREATE INDEX idx_screen_sessions_last_ping ON screen_sessions(last_ping DESC);
```

**Tabela de Logs de Tela (screen\_logs)**

```sql
-- Criar tabela de logs de tela
CREATE TABLE screen_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES screen_sessions(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Políticas de segurança
GRANT SELECT ON screen_logs TO anon;
GRANT ALL PRIVILEGES ON screen_logs TO authenticated;

-- Índices
CREATE INDEX idx_screen_logs_session_id ON screen_logs(session_id);
CREATE INDEX idx_screen_logs_event_type ON screen_logs(event_type);
CREATE INDEX idx_screen_logs_created_at ON screen_logs(created_at DESC);
```

**Dados Iniciais**

```sql
-- Inserir usuário administrador padrão
INSERT INTO users (email, password_hash, name, role) VALUES 
('<EMAIL>', '$2b$10$exemplo_hash_senha', 'Administrador', 'admin');

-- Inserir usuário cliente de exemplo
INSERT INTO users (email, password_hash, name, role) VALUES 
('<EMAIL>', '$2b$10$exemplo_hash_senha', 'Cliente Exemplo', 'client');
```

